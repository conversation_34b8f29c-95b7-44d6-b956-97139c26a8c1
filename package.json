{"name": "messaging-service-api", "version": "1.0.0", "main": "app.js", "scripts": {"start": "npx sequelize-cli db:migrate & nodemon app.js "}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@sendgrid/mail": "^7.7.0", "axios": "^1.3.2", "body-parser": "^1.20.0", "dotenv": "^16.0.0", "express": "^4.17.3", "multer": "^1.4.4", "mysql": "^2.18.1", "mysql2": "^2.3.3", "nodemon": "^2.0.15", "request": "^2.88.2", "sequelize": "^6.20.1", "sequelize-cli": "^6.6.0", "twilio": "^3.76.0"}, "repository": {"type": "git", "url": "git+https://github.com/BabbanGonaDev/communications-micro-service.git"}, "bugs": {"url": "https://github.com/BabbanGonaDev/communications-micro-service/issues"}, "homepage": "https://github.com/BabbanGonaDev/communications-micro-service#readme", "description": ""}