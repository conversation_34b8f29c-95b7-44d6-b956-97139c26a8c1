const express = require("express");
const { sendMessage, sendBulkMessage, trackCallBackStatus, sendTransactionalMessage,sendOtpByTermii,verifyOtpCode } = require("../controller/sms.controller-termii");
const router = express.Router();

// SMS sending route
router.route("/single").post(sendMessage);
router.route("/multiple").post(sendBulkMessage);
router.route("/transactional/single").post(sendTransactionalMessage);
router.route("/otp").post(sendOtpByTermii);
router.route("/otp/verify").post(verifyOtpCode);

module.exports = router;
