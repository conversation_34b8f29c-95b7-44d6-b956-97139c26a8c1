const database = require("../config/database.config");
const { getLogModel } = require("./models/logModels")
const { getTemplateModel, getWhatsappTemplateModel } = require("./models/whatsappTemplateModel")
const { Sequelize, DataTypes } = require("sequelize");

const sequelize = new Sequelize(database.DB, database.USER, database.PASSWORD, {
  host: database.HOST,
  dialect: database.dialect,
  operatorsAliases: false,

  pool: {
    max: database.pool.max,
    min: database.pool.min,
    acquire: database.pool.acquire,
    idle: database.pool.idle,
  },
});

sequelize
  .authenticate()
  .then(() => {
    console.log("connected");
  })
  .catch((err) => {
    console.log("Error" + err);
  });

const db = {};

db.Sequelize = Sequelize;
db.sequelize = sequelize;

db.logs = getLogModel(sequelize, DataTypes);
db.whatsappTemplate = getWhatsappTemplateModel(sequelize, DataTypes)

db.sequelize.sync({ force: false }).then(() => {
  console.log("yes re-sync done!");
});
module.exports = db;
