runtime: nodejs
service: communications-micro-service
env: flex

# This sample incurs costs to run on the App Engine flexible environment.
# The settings below are to reduce costs during testing and are not appropriate
# for production use. For more information, see:
# https://cloud.google.com/appengine/docs/flexible/nodejs/configuring-your-app-with-app-yaml
manual_scaling:
  instances: 1
resources:
  cpu: 1
  memory_gb: 2
  disk_size_gb: 10
env_variables:
  ACCOUNT_SID: "**********************************" 
  #AUTH_TOKEN:  "${{ secrets.AUTH_TOKEN }}"
  #BABBAN_GONA_SERVICE_ID:  "${{ secrets.BABBAN_GONA_SERVICE_ID }}"
  AUTH_TOKEN: "bbdbec40eb9f7eda4c6046a5cd20fc35"  
  BABBAN_GONA_SERVICE_ID: "MG0d7a29cc17292702b28eda372b5122de" 
  SENDGRID_API_KEY: '*********************************************************************'
  BABBAN_GONA_SERVICE_TERMII_ID: "BABBANGONA"
  TERMII_API_KEY: "TLcvICoWLpTYHNa1ZOV6atvN5AiZfV6MlUyOpoJUpo7ZrintSp3zCn43rIGWaE"
  BABBAN_GONA_SERVICE_EMAIL: <EMAIL>
  DB_PORT: 3306
  DB_HOST: *************
  DB_USER: "sms-service"
  DB_PASS: "c.@>G]&'4(}aYS}I"
  MYSQL_DB: "communication_service"
  SERVER_HOST: "https://communications-micro-service-dot-babbangona-dev.ey.r.appspot.com"
  BG_WHATSAPP_SENDER: "+***********"
  NODE_ENV: "development"
  PURE_TEXT_TEMPLATE_ID: "d-d1624ed54d7c4bffafa2e3e2070a8183"
  CALLBACK_SERVICE_API_BASE_URL: "http://**************/v1/communications/callback/service/"
#  CALLBACK_SERVICE_API_BASE_URL: "https://0393-102-89-34-22.eu.ngrok.io/v1/communications/callback/service/"
