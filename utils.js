/**
 * Converts a string of delimiter seperated values into a list of those trimmed string values
 * @param {String} str
 */
const splitString = (str, delimiter = ",") => {
  if (!str) {
    return [];
  }

  const list = str.split(delimiter);
  // trim all contents
  for (i in list) {
    list[i] = list[i].trim();
  }
  return list;
};

const isNotTransactional = message => {
  const blacklistedWords = [
    "Discount",
    "Offer",
    "Promo",
    "Limited time",
    "Buy now",
    "Save",
    "Deal",
    "Exclusive",
    "% off",
    "Act now",
    "Special",
    "Amazing",
    "Cashback",
    "Earn",
    "Double your",
    "Win",
    "Prize",
    "Congratulations",
    "Call now",
    "Subscribe",
    "Click here",
    "Shop now",
    "Order now",
    "Once in a lifetime",
    "Best value",
    "Massive savings",
    "Huge discounts",
    "Blockbuster",
    "One-time offer",
    "Extraordinary savings",
    "Don't wait",
    "Limited quantity",
    "Exclusive savings",
    "Hot price",
    "Big markdown",
    "Super deal",
    "Discounted items",
    "Going fast",
    "Clearance blowout",
    "Ultimate sale",
    "Get it now",
    "Superb offer",
    "Warehouse sale",
    "Lowest cost",
    "Reduced prices",
    "Special discount",
    "Early bird offer",
    "Savings event",
    "Summer sale",
    "Discounted products",
    "Unmissable deal",
    "Discounted rates",
    "Clearance sale",
    "Save big",
    "Extra savings",
    "Big sale",
    "Last chance",
    "Don't miss out",
    "Lowest price",
    "Bargain",
    "Hot deal",
    "Best price",
    "Double discount",
    "Incredible deal",
    "Free shipping",
    "Limited supply",
    "Save up to",
    "Flash sale",
    "Lowest ever",
    "Act fast",
    "Exclusive",
    "Stock up",
    "Limited Offer",
    "Special promotion",
    "Best deal",
    "Once-in-a-lifetime offer",
    "Discount code",
    "Holiday sale",
    "Price slash",
    "Happy birthday",
    "Merry Christmas",
    "Happy New Year",
    "Happy Easter",
    "Eid el",
    "Ramadan kareem",
    "Period of ramadan",
    "Fasting",
    "Best wishes",
    "Unsubscribe",
    "Manage email preferences",
    "occasion",
    "anniversary",
    "trade fair",
  ];

  const result = blacklistedWords.some(word => message.includes(word.toLowerCase()));
  return result;
};

const setMessageInterval = minutes => {
  const currentTime = new Date();
  const messageInterval = new Date(currentTime.getTime() - minutes * 60000);
  return messageInterval;
};

const formatPhoneNumber = number => {
  stringNumber = String(number); // Convert to string in case number is not a string

  if (stringNumber.length === 11 && stringNumber.startsWith("0")) {
    // Sample case: 08090000011
    return stringNumber.replace("0", "234");
  }

  if (stringNumber.length === 14 && stringNumber.startsWith("+234")) {
    // Sample case: +2348090000011
    return stringNumber.replace("+", "");
  }

  if (stringNumber.length === 13 && stringNumber.startsWith("234")) {
    // Sample case: 2348090000011
    return stringNumber;
  }

  if (stringNumber.length === 10 && !stringNumber.startsWith("0")) {
    // Sample case: 8090000011
    stringNumber = "234" + stringNumber;
    return stringNumber;
  }
  return false;
};

const validatePhoneNumber = number => {
  if (typeof number !== "string" || !number) {
    throw new CustomError(400, "Phone number must be a non-empty string");
  }

  if (number.length < 13 || number.length > 14) {
    throw new CustomError(400, "Phone number is invalid");
  }

  if (!number.startsWith("+234") && !number.startsWith("234")) {
    throw new CustomError(400, "Phone number is invalid");
  }
};

const validateMessage = message => {
  if (!message.trim()) {
    throw new CustomError(400, "Message is empty.");
  }

  if (!message.toLowerCase().includes("powered by babban gona")) {
    throw new CustomError(400, "Message is invalid. Kindly ensure it conforms to the required format.");
  }

  if (isNotTransactional(message.toLowerCase())) {
    throw new CustomError(400, "Message is most likely not transactional. Kindly check to confirm.");
  }
};

module.exports = {
  splitString,
  isNotTransactional,
  setMessageInterval,
  formatPhoneNumber,
  validatePhoneNumber,
  validateMessage,
};
