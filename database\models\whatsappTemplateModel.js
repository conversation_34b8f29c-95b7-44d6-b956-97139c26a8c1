const getWhatsappTemplateModel = (sequelize, DataTypes) => {
    // SCHEMA MODEL
    const Template = sequelize.define(
        "whatsapp_template",
        {
            templateName: {
                type: DataTypes.STRING,
                allowNull: false,
                primaryKey: true,
            },
            messageBody: {
                type: DataTypes.TEXT,
                allowNull: false,
            }
        }
    );

    return Template;
};

module.exports = { getWhatsappTemplateModel }
