const WhatsappService = require("../service/whatsapp.service");
const LogRepository = require("../../../database/repository/log.repository");
const WhatsappTemplateRepository = require("../../../database/repository/whatsapp.template.repository");
const Constants = require("../../../constants");
const Utils = require("../../../utils");

const sendFreeFormMessage = async (req, res) => {
    try {
        // retrieve message metadata
        const { number, message } = req.body;

        if (!number || !message) {
            return res.status(400).json({
                status: Constants.STATUS_FAILED,
                message: "number and message are required",
            });
        }

        const sentMessage = await WhatsappService.sendMessage(number, message);
        const messageType = Constants.LOG_TYPE_WHATSAPP;

        if (sentMessage.from === null) {
            sentMessage.from = Constants.DEFAULT_SENDER;
        }

        // let statusUpdate = Constants.STATUS_SUCCESS;
        // if (sentMessage.status === "sent" || sentMessage.status === "queued") {
        //     statusUpdate = Constants.STATUS_SUCCESS;
        // }

        let logMessageInfo = {
            sid: sentMessage.sid,
            direction: sentMessage.direction,
            sender: sentMessage.from,
            recipient: sentMessage.to,
            body: sentMessage.body,
            type: messageType,
        };

        // save message response to DB
        await LogRepository.addLog(logMessageInfo);

        return res.status(200).json({
            status: Constants.STATUS_SUCCESS,
            message: "whatsapp message sent successfully",
            body: sentMessage.body,
            recipient: sentMessage.to,
            sid: sentMessage.sid,
        });
    } catch (error) {
        console.log(error.message);
        res.status(400).json({
            status: Constants.STATUS_FAILED,
            message: "unable to send message",
        });
    }
}

const sendTemplateMessage = async (req, res) => {
    try {
        // retrieve message metadata
        let { number, templateName, templateData } = req.body;

        if (!templateName || !number) {
            return res.status(400).json({
                status: Constants.STATUS_FAILED,
                message: "number and templateName are required",
            });
        }

        // fetch template
        var template = await WhatsappTemplateRepository.findByPK(templateName)
        if (template == null) {
            return res.status(400).json({
                status: Constants.STATUS_FAILED,
                message: `Template with templateName ${templateName} not found`,
            });
        }

        // Get required number of template data
        const requiredDataCount = template.messageBody.match(new RegExp("{{.}}", "g")).length;

        // Get given template data
        const templateDataList = Utils.splitString(templateData);

        // Template data size mismatch
        if (requiredDataCount != templateDataList.length) {
            return res.status(400).json({
                status: Constants.STATUS_FAILED,
                message: `Incorrect templateData, expected ${requiredDataCount} arguments but got ${templateDataList.length}`,
            });
        }

        const sentMessage = await WhatsappService.sendTemplateMessage(number, template.messageBody, templateDataList);
        const messageType = Constants.LOG_TYPE_WHATSAPP;

        if (sentMessage.from === null) {
            sentMessage.from = Constants.DEFAULT_SENDER;
        }

        // let statusUpdate = Constants.STATUS_SUCCESS;
        // if (sentMessage.status === "sent" || sentMessage.status === "queued") {
        //     statusUpdate = Constants.STATUS_SUCCESS;
        // }

        let logMessageInfo = {
            sid: sentMessage.sid,
            direction: sentMessage.direction,
            sender: sentMessage.from,
            recipient: sentMessage.to,
            body: sentMessage.body,
            type: messageType,
        };

        // save message response to DB
        await LogRepository.addLog(logMessageInfo);

        return res.status(200).json({
            status: Constants.STATUS_SUCCESS,
            message: "whatsapp message sent successfully",
            body: sentMessage.body,
            recipient: sentMessage.to,
            sid: sentMessage.sid,
        });
    } catch (error) {
        console.log(error.message);
        res.status(400).json({
            status: Constants.STATUS_FAILED,
            message: "unable to send message",
        });
    }
}

// This is now being handled by callback service
// const trackCallBackStatus = async (req, res) => {
//     const messageSid = req.body.MessageSid;
//     const messageStatus = req.body.MessageStatus;
//     try {
//         console.log("Whatsapp track status callback called")
//         // update message status in database
//         const updateBody = { status: messageStatus };
//         const conditionBody = { sid: messageSid };
//         const updatedLog = await LogRepository.updateLog(updateBody, conditionBody);
//         console.log(updatedLog);

//         WhatsappService.trackCallBackStatus(messageSid, messageStatus);
//     } catch (error) {
//         throw error;
//     }
// }

module.exports = {
    sendFreeFormMessage,
    sendTemplateMessage,
};
