apiVersion: apps/v1
kind: Deployment
metadata:
  name: communications-micro-service-depl
spec:
  replicas: 1
  selector:
    matchLabels:
      app: communications-micro-service
  template:
    metadata:
      labels:
        app: communications-micro-service
    spec:
      containers:
        - name: communications-micro-service 
          image: REGISTRY_HOSTNAME/GKE_PROJECT/REPOSITORY_NAME/IMG:GITHUB_SHA
          ports:
          - containerPort: 80
          # ... other container configuration
          env:
          - name: ACCOUNT_SID
            valueFrom:
              secretKeyRef:
                name: communications-service-secrets
                key: ACCOUNT_SID
          - name: AUTH_TOKEN
            valueFrom:
              secretKeyRef:
                name: communications-service-secrets
                key: AUTH_TOKEN
          - name: BABBAN_GONA_SERVICE_ID
            valueFrom:
              secretKeyRef:
                name: communications-service-secrets
                key: BABBAN_GONA_SERVICE_ID
          - name: MYSQL_DB
            valueFrom:
              secretKeyRef:
                name: communications-service-secrets
                key: MYSQL_DB
          - name: DB_PORT
            valueFrom:
              secretKeyRef:
                name: communications-service-secrets
                key: DB_PORT
          - name: DB_HOST
            valueFrom:
              secretKeyRef:
                name: communications-service-secrets
                key: DB_HOST
          - name: DB_USER
            valueFrom:
              secretKeyRef:
                name: communications-service-secrets
                key: DB_USER
          - name: DB_PASS
            valueFrom:
              secretKeyRef:
                name: communications-service-secrets
                key: DB_PASS
          - name: SERVER_HOST
            valueFrom:
              secretKeyRef:
                name: communications-service-host-secrets
                key: SERVER_HOST
          - name: PORT
            valueFrom:
              secretKeyRef:
                name: deployment-port
                key: PORT
          - name: SENDGRID_API_KEY
            valueFrom:
              secretKeyRef:
                name: sendgrid-api-key
                key: SENDGRID_API_KEY
          - name: BABBAN_GONA_SERVICE_EMAIL
            valueFrom:
              secretKeyRef:
                name: service-email
                key: BABBAN_GONA_SERVICE_EMAIL
          - name: BABBAN_GONA_SERVICE_TERMII_ID
            valueFrom:
              secretKeyRef:
                name: termii-details
                key: BABBAN_GONA_SERVICE_TERMII_ID
          - name: TERMII_API_KEY
            valueFrom:
              secretKeyRef:
                name: termii-details
                key: TERMII_API_KEY
          - name: BG_WHATSAPP_SENDER
            valueFrom:
              secretKeyRef:
                name: callback-details
                key: BG_WHATSAPP_SENDER
          - name: CALLBACK_SERVICE_API_BASE_URL
            valueFrom:
              secretKeyRef:
                name: callback-details
                key: CALLBACK_SERVICE_API_BASE_URL
          - name: TRANSACTIONAL_TERMII_ID
            valueFrom:
              secretKeyRef:
                name: transaction-termii-id
                key: TRANSACTIONAL_TERMII_ID
