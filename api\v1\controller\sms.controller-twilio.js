const TwilioSmsService = require("../service/sms.service-twilio");
const LogRepository = require("../../../database/repository/log.repository");
const Constants = require("../../../constants");

  
const sendMessage = async (req, res) => {
  // retrieve message metadata
  const { number, message } = req.body;

  if (!number) {
    return res.status(400).json({
      status: "failed",
      message: "no phone number found",
    });
  } else if (!message) {
    return res.status(400).json({
      status: "failed",
      message: "no message found",
    });
  }

  try {
    const sentMessage = await TwilioSmsService.sendMessage(number, message);
    const messageType = "SMS";

    if (sentMessage.from === null) {
      sentMessage.from = "BABBANGONA";
    }

    // let statusUpdate;
    // if (sentMessage.status === "accepted") {
    //   statusUpdate = "success";
    // }

    let logMessageInfo = {
      sid: sentMessage.sid,
      direction: sentMessage.direction,
      sender: sentMessage.from,
      recipient: sentMessage.to,
      body: sentMessage.body,
      type: messageType,
    };

    // save message response to DB
    await LogRepository.addLog(logMessageInfo);

    return res.status(200).json({
      status: Constants.STATUS_SUCCESS,
      message: "message sent succesfully",
      info: "message sent succesfully",
      body: sentMessage.body,
      recipient: sentMessage.to,
      sid: sentMessage.sid,
    });
  } catch (error) {
    console.log(error.message);
    res.status(400).json({
      status: Constants.STATUS_FAILED,
      message: "unable to send message",
    });
  }
}

// This is now being handled by callback service
// const trackCallBackStatus = async (req, res) => {
//   const messageSid = req.body.MessageSid;
//   const messageStatus = req.body.MessageStatus;
//   try {
//     // update message status in database
//     const updateBody = { status: messageStatus };
//     const conditionBody = { sid: messageSid };
//     const updatedLog = await LogRepository.updateLog(updateBody, conditionBody);
//     console.log(updatedLog);

//     TwilioSmsService.trackCallBackStatus(messageSid, messageStatus);
//   } catch (error) {
//     throw error;
//   }
// }

module.exports = {
  sendMessage,
  // trackCallBackStatus,
};
