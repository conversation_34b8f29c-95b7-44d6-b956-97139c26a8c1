const WhatsappTemplateRepository = require("../../../database/repository/whatsapp.template.repository");
const EmailService = require("../service/email.service");
const Constants = require("../../../constants");


const addWhatsappTemplate = async (req, res) => {
    try {

        // retrieve payload content
        const { templateName, messageBody } = req.body;

        // missing required attributes
        if (!templateName || !messageBody) {
            return res.status(400).json({
                status: Constants.STATUS_FAILED,
                message: "templateName and messageBody are required",
            });
        }

        // duplicate templateId for same type
        var existingTemplate = await WhatsappTemplateRepository.findByPK(templateName)
        if (existingTemplate != null) {
            return res.status(400).json({
                status: Constants.STATUS_FAILED,
                message: `Template with templateName ${templateName} already exists`,
            });
        }

        // save new template
        const template = {
            templateName,
            messageBody
        };
        await WhatsappTemplateRepository.addTemplate(template);

        return res.status(200).json({
            status: Constants.STATUS_SUCCESS,
            message: "Template added succesfully",
            data: template
        });

    } catch (error) {
        console.log(error.message);
        res.status(400).json({
            status: "failed",
            message: "An unknown error occured while adding new template",
        });
    }
}

const getSingleWhatsappTemplate = async (req, res) => {
    try {

        // retrieve payload content
        const { templateName } = req.query;

        // missing required attributes
        if (!templateName) {
            return res.status(400).json({
                status: "failed",
                message: "templateName is required",
            });
        }

        var template = await WhatsappTemplateRepository.findByPK(templateName)
        return res.status(200).json({
            status: "success",
            message: "Template fetched succesfully",
            data: template
        });

    } catch (error) {
        console.log(error.message);
        res.status(400).json({
            status: "failed",
            message: "An unknown error occured while fetching template",
        });
    }
}

const updateWhatsappTemplate = async (req, res) => {
    try {

        // retrieve payload content
        let { templateName, messageBody } = req.body;

        if (!templateName) {
            templateName = req.query.templateName;
        }

        // missing required attributes
        if (!templateName && !messageBody) {
            return res.status(400).json({
                status: "failed",
                message: "templateName and messageBody are required",
            });
        }

        var template = await WhatsappTemplateRepository.findByPK(templateName)
        if (template == null) {
            return res.status(400).json({
                status: "failed",
                message: `Template with templateName ${templateName} not found`,
            });
        }

        await WhatsappTemplateRepository.updateTemplate({ messageBody }, { templateName })
        template = await WhatsappTemplateRepository.findByPK(templateName)

        return res.status(200).json({
            status: "success",
            message: "Template updated succesfully",
            data: template
        });

    } catch (error) {
        console.log(error.message);
        res.status(400).json({
            status: "failed",
            message: "An unknown error occured while updating template",
        });
    }
}

const getAllWhatsappTemplates = async (req, res) => {
    try {
        var templates = await WhatsappTemplateRepository.findAll({})
        return res.status(200).json({
            status: "success",
            message: "Templates fetched succesfully",
            data: templates
        });

    } catch (error) {
        console.log(error.message);
        res.status(400).json({
            status: "failed",
            message: "An unknown error occured while fetching templates",
        });
    }
}

const deleteWhatsappTemplates = async (req, res) => {
    try {
        // retrieve payload content
        const { templateName } = req.query;

        // missing required attributes
        if (!templateName) {
            return res.status(400).json({
                status: "failed",
                message: "templateName is required",
            });
        }

        await WhatsappTemplateRepository.deleteAll({ templateName })
        return res.status(200).json({
            status: "success",
            message: "Template deleted succesfully"
        });

    } catch (error) {
        console.log(error.message);
        res.status(400).json({
            status: "failed",
            message: "An unknown error occured while deleting template",
        });
    }
}

const getEmailTemplates = async (req, res) => {
    try {
        const response = await EmailService.getEmailTemplates();
        const templates = [];
        for (let template of response.body.result) {
            const emailTemplate = {
                id: template.id,
                name: template.name,
                generation: template.generation,
            }

            // get active version
            for (let version of template.versions) {
                if (version.active == 1) {
                    emailTemplate.templateId = version.template_id
                    let thumbnailUrl = version.thumbnail_url;
                    if (thumbnailUrl.startsWith("//")) {
                        thumbnailUrl = thumbnailUrl.substring(2);
                    }
                    emailTemplate.thumbnailUrl = thumbnailUrl;
                }
            }

            templates.push(emailTemplate)
        }

        return res.status(200).json({
            status: "success",
            message: "Email templates fetched succesfully",
            data: templates
        });

    } catch (error) {
        console.log(error);
        res.status(400).json({
            status: "failed",
            message: "An unknown error occured while fetching template",
        });
    }
}


module.exports = {
    addWhatsappTemplate,
    getSingleWhatsappTemplate,
    updateWhatsappTemplate,
    deleteWhatsappTemplates,
    getAllWhatsappTemplates,
    getEmailTemplates,
}

