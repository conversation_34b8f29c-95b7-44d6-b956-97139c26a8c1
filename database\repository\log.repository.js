const db = require("../database");

// create main Model
const Log = db.logs;

/**
 * Insert single Log data
 * @param {*} data 
 * @returns 
 */
const addLog = async (data) => {
  try {
    const log = await Log.create(data);
    return log;
  } catch (error) {
    throw error;
  }
}

/**
 * Insert bulk records
 * @param {*} data 
 * @returns 
 */
const addLogs = async (data) => {
  try {
    const log = await Log.bulkCreate(data);
    return log;
  } catch (error) {
    throw error;
  }
}

const updateLog = async (updateBody, conditionBody) => {
  try {
    const updatedLog = await Log.update(updateBody, {
      where: conditionBody,
    });
    return updatedLog;
  } catch (error) {
    throw error;
  }
}

const findAll = async (conditionBody) => {
  try {
      return await Log.findAll({ where: conditionBody });
  } catch (error) {
      throw error;
  }
}

module.exports = {
  addLog,
  addLogs,
  updateLog,
  findAll,
};
