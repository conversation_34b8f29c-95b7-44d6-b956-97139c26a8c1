const { twilioClient } = require("../../../config/clients.config");

const sendMessage = async (number, message) => {
    try {
        const messageResponse = await twilioClient.messages.create({
            body: message,
            from: `whatsapp:${process.env.BG_WHATSAPP_SENDER}`, // From Babban Gona communication service ID
            statusCallback: `${process.env.CALLBACK_SERVICE_API_BASE_URL}/sms/twilio`, // track message delivery status
            to: `whatsapp:${number}`, // Text this number
        });

        return messageResponse;
    } catch (error) {
        throw error;
    }
}

/**
 * Merges templateData and templateBody to form a message
 * Sends the message via whatsapp
 * @param {String} number 
 * @param {String} templateBody 
 * @param {Array} templateData 
 */
const sendTemplateMessage = async (number, templateBody, templateData) => {
    let message = templateBody;
    for (i in templateData) {
        const placeholder = `{{${Number(i) + 1}}}`
        message = message.replace(placeholder, templateData[i])
    }
    return sendMessage(number, message)
}

const trackCallBackStatus = (messageSid, messageStatus) => {
    console.log(`SID: ${messageSid}, Status: ${messageStatus}`);
}

module.exports = {
    sendMessage,
    sendTemplateMessage,
    trackCallBackStatus,
};
