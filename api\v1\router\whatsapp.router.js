const express = require("express");
const { sendFreeFormMessage, sendTemplateMessage, trackCallBackStatus } = require("../controller/whatsapp.controller");
const router = express.Router();

// Send free form message
router.route("/free-form").post(sendFreeFormMessage);

// Send template message
router.route("/template").post(sendTemplateMessage);

// Get status update from Twilio
// router.route("/status").post(trackCallBackStatus); now being handled by callback service

module.exports = router;
