const { twilioClient } = require("../../../config/clients.config");
require("dotenv/config");

/**
 *
 * @param { Number } string number string that message will be sent to
 * @param { Message } string message string that will be sent to number
 * @description - This function does the following
 * - send message from Babban Gona message service to Users through their numbers
 * - It accepts a number string as an argument
 * - It accepts a message string as an argument
 */
const sendMessage = async (number, message) => {
  try {
    const messageResponse = await twilioClient.messages.create({
      body: message,
      from: process.env.BABBAN_GONA_SERVICE_ID, // From Babban Gona communication service ID
      statusCallback: `${process.env.CALLBACK_SERVICE_API_BASE_URL}/sms/twilio`, // track message delivery status
      to: number, // Text this number
    });

    return messageResponse;
  } catch (error) {
    throw error;
  }
}

const trackCallBackStatus = (messageSid, messageStatus) => {
  console.log(`SID: ${messageSid}, Status: ${messageStatus}`);
}

module.exports = {
  sendMessage,
  trackCallBackStatus,
};
