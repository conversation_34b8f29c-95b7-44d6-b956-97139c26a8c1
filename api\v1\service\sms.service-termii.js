const { twilioClient } = require("../../../config/clients.config");
const axios = require("axios");
const { isNotTransactional, setMessageInterval, formatPhoneNumber } = require("../../../utils");
const CustomError = require("../error");
require("dotenv/config");
const LogRepository = require("../../../database/repository/log.repository");
const Constants = require("../../../constants");
const { Op } = require("sequelize");

/**
 *
 * @param { Number } string number string that message will be sent to
 * @param { Message } string message string that will be sent to number
 * @description - This function does the following
 * - send message from Babban Gona message service to Users through their numbers
 * - It accepts a number string as an argument
 * - It accepts a message string as an argument
 */
const sendMessage = async (number, message) => {
  const data = {
    to: number,
    // from: process.env.BABBAN_GONA_SERVICE_ID, // From Babban Gona communication service ID
    from: process.env.BABBAN_GONA_SERVICE_TERMII_ID, // From Babban Gona communication service ID
    sms: message,
    type: "plain",
    api_key: process.env.TERMII_API_KEY,
    channel: "generic",
  };
  const headers = {
    "Content-Type": ["application/json", "application/json"],
  };
  // const options = {
  //   method: "POST",
  //   url: "https://api.ng.termii.com/api/sms/send",
  //   headers: {
  //     "Content-Type": ["application/json", "application/json"],
  //   },
  //   body: JSON.stringify(data),
  // };

  return axios.post("https://api.ng.termii.com/api/sms/send", data, headers);

  // request(options, function (error, response) {
  //   if (error) {
  //     return callBack(error);
  //   }
  //   // console.log(response.body);
  //   return callBack(null, response.body);
  // });
};

const sendBulkMessage = (recipientNumberList, message) => {
  const data = {
    to: recipientNumberList,
    // from: process.env.BABBAN_GONA_SERVICE_ID, // From Babban Gona communication service ID
    from: process.env.BABBAN_GONA_SERVICE_TERMII_ID, // From Babban Gona communication service ID
    sms: message,
    type: "plain",
    api_key: process.env.TERMII_API_KEY,
    channel: "generic",
  };
  const headers = {
    "Content-Type": ["application/json", "application/json"],
  };
  return axios.post("https://api.ng.termii.com/api/sms/send/bulk", data, headers);
};

const sendTransactionalMessage = async (number, message) => {
  if (!number.trim()) {
    throw new CustomError(400, "Please provide phone number.");
  }

  if (!message.trim()) {
    throw new CustomError(400, "Message is empty.");
  }

  if (!message.toLowerCase().includes("powered by babban gona")) {
    message = message + "\nPowered By Babban Gona";
    // throw new CustomError(400, "Message is invalid. Kindly ensure it conforms to the required format.");
  }

  if (isNotTransactional(message.toLowerCase())) {
    throw new CustomError(400, "Message is most likely not transactional. Kindly check to confirm.");
  }

  const formattedPhoneNumber = formatPhoneNumber(number);

  if (!formattedPhoneNumber) {
    throw new CustomError(400, "Phone number is not valid");
  }

  const messageInterval = setMessageInterval(Constants.SMS_RESTRICTION_TIME);
  const dbResult = await LogRepository.findAll({
    recipient: formattedPhoneNumber,
    type: Constants.LOG_TYPE_SMS,
    createdAt: {
      [Op.gte]: messageInterval,
    },
  });

  const recipientMessages = dbResult.map(item => item.body);

  if (recipientMessages.includes(message)) {
    throw new CustomError(400, "Possible duplicate message, kindly retry after 2 - 3 minutes.");
  }
  const data = {
    to: formattedPhoneNumber,
    from: process.env.TRANSACTIONAL_TERMII_ID,
    sms: message,
    type: "plain",
    api_key: process.env.TERMII_API_KEY,
    channel: "dnd",
  };

  const response = await axios.post("https://api.ng.termii.com/api/sms/send", data);
  let result = response.data;
  result.direction = "outbound-api";
  result.body = message;
  result.to = formattedPhoneNumber;

  const messageType = "SMS";

  if (result.user === "Babban Gona") {
    result.user = "BABBANGONA";
  }

  let logMessageInfo = {
    sid: result.message_id,
    direction: result.direction,
    sender: result.user,
    recipient: result.to,
    body: result.body,
    type: messageType,
  };

  // save message response to DB
  try {
    LogRepository.addLog(logMessageInfo);
  } catch (error) {
    return res.status(400).json({
      status: Constants.STATUS_FAILED,
      message: "unable to log message record to database",
      info: "unable to log message record to database",
      error: error.message,
    });
  }
  return {
    status: Constants.STATUS_SUCCESS,
    message: "message sent successfully",
    info: "message sent successfully",
    body: result.body,
    recipient: result.to,
    sid: result.message_id,
  };
};

const sendBulkTransactionalMessage = (numberList, message) => {
  if (!numberList) {
    throw new CustomError(400, "Please provide list of phone numbers.");
  }

  if (!message.trim()) {
    throw new CustomError(400, "Message is empty.");
  }

  if (!message.toLowerCase().includes("powered by babban gona")) {
    throw new CustomError(400, "Message is invalid. Kindly ensure it conforms to the required format.");
  }

  if (isNotTransactional(message)) {
    throw new CustomError(400, "Message is most likely not transactional. Kindly check to confirm.");
  }
  const data = {
    to: numberList,
    from: process.env.TRANSACTIONAL_TERMII_ID,
    sms: message,
    type: "plain",
    api_key: process.env.TERMII_API_KEY,
    channel: "dnd",
  };

  return axios.post("https://api.ng.termii.com/api/sms/send/bulk", data);
};

const trackCallBackStatus = (messageSid, messageStatus) => {
  console.log(`SID: ${messageSid}, Status: ${messageStatus}`);
};

const sendOtpByTermii = async phoneNumber => {
  if (!String(phoneNumber).trim()) {
    throw new CustomError(400, "Please provide phone number.");
  }

  const formattedPhoneNumber = formatPhoneNumber(phoneNumber);

  if (!formattedPhoneNumber) {
    throw new CustomError(400, "Phone number is not valid");
  }
  const messageInterval = setMessageInterval(Constants.SMS_RESTRICTION_TIME);
  const dbResult = await LogRepository.findAll({
    recipient: formattedPhoneNumber,
    type: Constants.LOG_TYPE_OTP,
    createdAt: {
      [Op.gte]: messageInterval, // Find records updated after two minutes ago
    },
  });

  if (dbResult.length) {
    throw new CustomError(400, "Possible duplicate message, kindly retry after 2 - 3 minutes.");
  }

  const body = {
    api_key: `${process.env.TERMII_API_KEY}`,
    message_type: "NUMERIC",
    to: `${formattedPhoneNumber}`,
    from: process.env.TRANSACTIONAL_TERMII_ID,
    channel: "dnd",
    pin_attempts: Constants.TERMII_OTP_NUMBER_OF_ATTEMPTS,
    pin_time_to_live: Constants.TERMII_OTP_LIFETIME,
    pin_length: Constants.TERMII_OTP_LENGTH,
    pin_placeholder: Constants.TERMII_OTP_PLACEHOLDER,
    message_text: `Your Babban Gona confirmation code is ${Constants.TERMII_OTP_PLACEHOLDER}. It expires in ${Constants.TERMII_OTP_LIFETIME} ${
      Constants.TERMII_OTP_LIFETIME > 1 ? "minutes" : "minute"
    }`,
    pin_type: "NUMERIC",
  };
  const url = "https://api.ng.termii.com/api/sms/otp/send";

  try {
    const response = await axios.post(url, body);
    const result = response.data;
    result.direction = "outbound-api";
    result.body = "OTP Sent from Termii";
    result.to = formattedPhoneNumber;
    result.user = "BABBANGONA";

    const messageType = "OTP";

    let logMessageInfo = {
      sid: result.pinId,
      direction: result.direction,
      sender: result.user,
      recipient: result.to,
      body: result.body,
      type: messageType,
    };

    // save message response to DB
    try {
      LogRepository.addLog(logMessageInfo);
    } catch (error) {
      return res.status(400).json({
        status: Constants.STATUS_FAILED,
        message: "unable to log message record to database",
        info: "unable to log message record to database",
        error: error.message,
      });
    }
    return {
      status: Constants.STATUS_SUCCESS,
      message: "message sent successfully",
      info: "message sent successfully",
      body: result.body,
      recipient: result.to,
      pinId: result.pinId,
    };
  } catch (err) {
    throw new CustomError(400, `Error sending otp to ${formattedPhoneNumber}`);
  }
};

const verifyOtpCode = async (pinId, otpCode) => {
  if (!pinId || !otpCode) {
    throw new CustomError(400, "Please provide pinId and otpCode");
  }

  if (otpCode.length !== Constants.TERMII_OTP_LENGTH) {
    throw new CustomError(400, "Invalid optCode");
  }
  const body = {
    api_key: `${process.env.TERMII_API_KEY}`,
    pin_id: `${pinId}`,
    pin: `${otpCode}`,
  };

  const url = "https://api.ng.termii.com/api/sms/otp/verify";

  try {
    const { data } = await axios.post(url, body);
    return data;
  } catch (err) {
    const { data } = err.response;
    if (data.status == 400) {
      return data;
    }
    throw new CustomError(400, "request not processed");
  }
};

module.exports = {
  sendMessage,
  sendBulkMessage,
  trackCallBackStatus,
  sendTransactionalMessage,
  sendBulkTransactionalMessage,
  sendOtpByTermii,
  verifyOtpCode,
};
