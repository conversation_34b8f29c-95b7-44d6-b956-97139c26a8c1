const db = require("../database");

// create main Model
const WhatsappTemplate = db.whatsappTemplate;

/**
 * Insert single Template data
 * @param {*} data 
 * @returns 
 */
const addTemplate = async (data) => {
    try {
        const template = await WhatsappTemplate.create(data);
        return template;
    } catch (error) {
        throw error;
    }
}

/**
 * Insert bulk records
 * @param {*} data 
 * @returns 
 */
const addTemplates = async (data) => {
    try {
        const templates = await WhatsappTemplate.bulkCreate(data);
        return templates;
    } catch (error) {
        throw error;
    }
}

const updateTemplate = async (updateBody, conditionBody) => {
    try {
        const status = await WhatsappTemplate.update(updateBody, {
            where: conditionBody,
        });
        return status;
    } catch (error) {
        throw error;
    }
}

const findOne = async (conditionBody) => {
    try {
        return await WhatsappTemplate.findOne({ where: conditionBody });
    } catch (error) {
        throw error;
    }
}

const findByPK = async (primaryKey) => {
    try {
        return await WhatsappTemplate.findByPk(primaryKey);
    } catch (error) {
        throw error;
    }
}

const findAll = async (conditionBody) => {
    try {
        return await WhatsappTemplate.findAll({ where: conditionBody });
    } catch (error) {
        throw error;
    }
}

const deleteAll = async (conditionBody) => {
    try {
        return await WhatsappTemplate.destroy({ where: conditionBody });
    } catch (error) {
        throw error;
    }
}

module.exports = {
    addTemplate,
    addTemplates,
    updateTemplate,
    findOne,
    findByPK,
    findAll,
    deleteAll
};
