/* Log types */
const LOG_TYPE_SMS = "SMS";
const LOG_TYPE_OTP = "OTP";
const LOG_TYPE_EMAIL = "EMAIL";
const LOG_TYPE_WHATSAPP = "WHATSAPP";
/* Log types */

/* Template types */
const TEMPLATE_TYPE_EMAIL = "EMAIL";
const TEMPLATE_TYPE_WHATSAPP = "WHATSAPP";
/* Template types */

/* Response statuses */
const STATUS_FAILED = 0;
const STATUS_SUCCESS = 1;
/* Response statuses */

/* Misc */
const DEFAULT_SENDER = "BABBANGONA";
const PURE_TEXT_TEMPLATE_ID = "d-d1624ed54d7c4bffafa2e3e2070a8183";
const DEFAULT_STATUS = "accepted";
const TERMII_OTP_LIFETIME = 5;
const TERMII_OTP_NUMBER_OF_ATTEMPTS = 10;
const TERMII_OTP_LENGTH = 4;
const TERMII_OTP_PLACEHOLDER = "< 1234 >";
const SMS_RESTRICTION_TIME = 3;
/* Misc */

module.exports = {
  LOG_TYPE_SMS,
  LOG_TYPE_OTP,
  LOG_TYPE_EMAIL,
  LOG_TYPE_WHATSAPP,
  TEMPLATE_TYPE_EMAIL,
  TEMPLATE_TYPE_WHATSAPP,
  STATUS_FAILED,
  STATUS_SUCCESS,
  DEFAULT_SENDER,
  PURE_TEXT_TEMPLATE_ID,
  DEFAULT_STATUS,
  TERMII_OTP_LIFETIME,
  TERMII_OTP_NUMBER_OF_ATTEMPTS,
  TERMII_OTP_LENGTH,
  TERMII_OTP_PLACEHOLDER,
  SMS_RESTRICTION_TIME,
};
