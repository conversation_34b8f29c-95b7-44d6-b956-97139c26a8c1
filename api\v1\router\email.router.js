const express = require("express");
const { trackEmailEventStatus, sendMultipleEmails, sendMultipleEmailsWithAttachment, sendPureTextEmail, sendTemplateEmail } = require("../controller/email.controller");

const router = express.Router();

// email status routes
// router.route("/status").post(trackEmailEventStatus); Now being handled by callback service

// send email routes
router.route("/text").post(sendPureTextEmail);
router.route("/template").post(sendTemplateEmail);
router.route("/multiple/attachment").post(sendMultipleEmailsWithAttachment);

module.exports = router;

