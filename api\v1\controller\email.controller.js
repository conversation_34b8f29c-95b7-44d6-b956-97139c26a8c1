require('dotenv')
const EmailService = require("../service/email.service");
const LogRepository = require("../../../database/repository/log.repository");
const Constants = require("../../../constants");
const Utils = require("../../../utils");


const sendMultipleEmailsWithAttachment = async (req, res) => {
  // retrieve emails metadata
  const { subject, message, emailList } = req.body;
  const attachment = req.file;
  emailList = JSON.parse(emailList);
  const messageType = "EMAIL";

  // request body input validation
  if (emailList.length === 0) {
    return res.status(400).json({
      status: "failed",
      message: "email list can't be empty",
    });
  } else if (!subject) {
    return res.status(400).json({
      status: "failed",
      message: "no email subject found",
    });
  } else if (!message) {
    return res.status(400).json({
      status: "failed",
      message: "no email message found",
    });
  }
  try {
    const sentEmails = await EmailService.sendMultipleEmailsWithAttachment(
      emailList,
      subject,
      message,
      attachment
    );

    // add extra email metadata to returned response
    sentEmails.direction = "outbound-api";
    sentEmails.from = process.env.BABBAN_GONA_SERVICE_EMAIL;
    sentEmails.body = message;
    const sid = sentEmails["headers"]["x-message-id"];
    sentEmails.sid = sid;

    // retrieve all recipient emails
    const recipientEmailList = [];
    for (const emailObject of emailList) {
      recipientEmailList.push(emailObject.email);
    }

    console.log(recipientEmailList);

    if (sentEmails.statusCode === 202) {
      sentEmails.status = "accepted";
    }

    // create email records model to be saved to DB
    const records = [];
    for (const emailObject of emailList) {
      let logEmailInfo = {
        sid: sentEmails.sid,
        direction: sentEmails.direction,
        sender: sentEmails.from,
        recipient: emailObject.email,
        body: sentEmails.body,
        type: messageType,
      };
      records.push(logEmailInfo);
    }

    // save email record logs to DB
    await LogRepository.addLogs(records);

    return res.status(200).json({
      status: Constants.STATUS_SUCCESS,
      message: "Emails sent successfully",
      recipients: recipientEmailList,
      sid: sentEmails.sid,
    });
  } catch (error) {
    console.log(error);
    res.status(400).json({
      status: Constants.STATUS_FAILED,
      message: "unable to send email",
      error: error,
    });
  }
}


const sendTemplateEmail = async (req, res) => {
  try {
    const { emailList, templateId, templateData } = req.body;
    if (!emailList || !templateId || !templateData) {
      return res.status(400).json({
        status: Constants.STATUS_FAILED,
        message: "emailList, templateId and templateData are required",
      });
    }

    // retrieve all recipient emails
    const recipientEmailList = Utils.splitString(emailList);
    const emailObjList = [];
    for (email of recipientEmailList) {
      emailObjList.push({
        email
      })
    }
    const response = await EmailService.sendTemplateEmail(emailObjList, templateId, templateData);
    console.log("Response is", response);

    // add extra email metadata to returned response
    response.direction = "outbound-api";
    response.from = process.env.BABBAN_GONA_SERVICE_EMAIL;
    response.body = JSON.stringify(templateData);
    const sid = response["headers"]["x-message-id"];
    response.sid = sid;

    if (response.statusCode === 202) {
      response.status = "accepted";
    }

    // create email records model to be saved to DB
    const records = [];
    for (const email of recipientEmailList) {
      let logEmailInfo = {
        sid: response.sid,
        direction: response.direction,
        sender: response.from,
        recipient: email,
        body: response.body,
        type: Constants.LOG_TYPE_EMAIL,
      };
      records.push(logEmailInfo);
    }

    // save email record logs to DB
    await LogRepository.addLogs(records);

    return res.status(200).json({
      status: Constants.STATUS_SUCCESS,
      message: "Emails successfully sent",
      recipients: recipientEmailList,
      sid: response.sid,
    });

  } catch (error) {
    console.log(error)
    res.status(400).json({
      status: Constants.STATUS_FAILED,
      message: "unable to send email",
      error: error,
    });
  }
}

const sendPureTextEmail = async (req, res) => {
  try {
    const { emailList, subject, message } = req.body;
    if (!emailList || !subject || !message) {
      return res.status(400).json({
        status: Constants.STATUS_FAILED,
        message: "emailList, subject and message are required",
      });
    }

    // retrieve all recipient emails
    const recipientEmailList = Utils.splitString(emailList);
    const emailObjList = [];
    for (email of recipientEmailList) {
      emailObjList.push({
        email
      })
    }

    let templateId = process.env.PURE_TEXT_TEMPLATE_ID;
    if (!templateId) {
      console.log("Getting template id from constants");
      templateId = Constants.PURE_TEXT_TEMPLATE_ID;
    }
    console.log("Template Id is ", templateId);

    let templateData = {
      subject,
      message
    }

    const response = await EmailService.sendTemplateEmail(emailObjList, templateId, templateData);
    console.log("Response is", response);

    // add extra email metadata to returned response
    response.direction = "outbound-api";
    response.from = process.env.BABBAN_GONA_SERVICE_EMAIL;
    response.body = JSON.stringify(templateData);
    const sid = response["headers"]["x-message-id"];
    response.sid = sid;

    if (response.statusCode === 202) {
      response.status = "accepted";
    }

    // create email records model to be saved to DB
    const records = [];
    for (const email of recipientEmailList) {
      let logEmailInfo = {
        sid: response.sid,
        direction: response.direction,
        sender: response.from,
        recipient: email,
        body: message,
        type: Constants.LOG_TYPE_EMAIL,
      };
      records.push(logEmailInfo);
    }

    // save email record logs to DB
    await LogRepository.addLogs(records);

    return res.status(200).json({
      status: Constants.STATUS_SUCCESS,
      message: "Emails successfully sent",
      recipients: recipientEmailList,
      sid: response.sid,
    });

  } catch (error) {
    console.log(error)
    res.status(400).json({
      status: Constants.STATUS_FAILED,
      message: "unable to send email",
      error: error,
    });
  }
}

module.exports = {
  sendMultipleEmailsWithAttachment,
  // trackEmailEventStatus,
  sendTemplateEmail,
  sendPureTextEmail,
}
