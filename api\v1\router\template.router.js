const express = require("express");
const {
    addWhatsappTemplate,
    getSingleWhatsappTemplate,
    updateWhatsappTemplate,
    getAllWhatsappTemplates,
    deleteWhatsappTemplates,
    getEmailTemplates,
} = require("../controller/template.controller");

const router = express.Router();

// whatsapp template routes
router.route("/whatsapp").get(getSingleWhatsappTemplate);
router.route("/whatsapp").post(addWhatsappTemplate);
router.route("/whatsapp").put(updateWhatsappTemplate);
router.route("/whatsapp").delete(deleteWhatsappTemplates);
router.route("/whatsapp/all").get(getAllWhatsappTemplates);

// email template routes
router.route("/email/all").get(getEmailTemplates);

module.exports = router;
