require("dotenv/config");
const express = require("express");
const app = express();
const bodyParser = require("body-parser");
const multer = require("multer");

//Routers

const TwilioSmsRouter = require("./api/v1/router/sms.router-twilio");
const TermiiSmsRouter = require("./api/v1/router/sms.router-termii");
const EmailRouter = require("./api/v1/router/email.router");
const WhatsappRouter = require("./api/v1/router/whatsapp.router");
const TemplateRouter = require("./api/v1/router/template.router");
const StatusRouter = require("./api/v1/router/status.router");

const multerMid = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 200 * 1024 * 1024,
  },
});

const port = process.env.PORT || 2000;

// middleware
app.use(bodyParser.json());
app.use(multerMid.single("attachment"));
app.use(bodyParser.urlencoded({ extended: true }));


// routers
app.use("/v1/twilio/sms", TwilioSmsRouter);
app.use("/v1/termii/sms", TermiiSmsRouter);
app.use("/v1/email", EmailRouter);
app.use("/v1/whatsapp", WhatsappRouter);
app.use("/v1/template", TemplateRouter);
app.use("/v1/status", StatusRouter);

// start up server
app.listen(port, () => console.log(`Server running on port ${port}`));
